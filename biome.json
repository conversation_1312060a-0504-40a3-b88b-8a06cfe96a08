{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "ignore": []}, "formatter": {"enabled": true, "useEditorconfig": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 4, "lineEnding": "lf", "lineWidth": 120, "attributePosition": "auto", "bracketSpacing": true}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "complexity": {"noForEach": "off", "useOptionalChain": "warn"}}}, "javascript": {"formatter": {"jsxQuoteStyle": "double", "quoteProperties": "asNeeded", "trailingCommas": "all", "semicolons": "always", "arrowParentheses": "always", "bracketSameLine": false, "quoteStyle": "double", "attributePosition": "auto", "bracketSpacing": true}}}