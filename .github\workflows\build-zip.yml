name: Build and Zip Artifacts

on:
    push:
        branches:
            - "master" # This will match all branches
    pull_request: # Optional: Run on pull requests too

jobs:
    build:
        runs-on: ubuntu-latest # Use Ubuntu for the build environment

        steps:
            # Step 1: Checkout the repository
            - name: Checkout code
              uses: actions/checkout@v4

            # Step 2: Set up Node.js
            - name: Set up Node.js
              uses: actions/setup-node@v4
              with:
                  node-version: "20" # Adjust to your project's Node.js version

            # Step 3: Install dependencies
            - name: Install dependencies
              run: npm install

            # Step 4: Build the project
            - name: Build the project
              run: npm run build:dev

            # Step 5: Get Project Name
            - name: Get Project Name
              id: get-project-name
              run: |
                  PROJECT_NAME=$(node -p "require('./package.json').name")
                  echo "PROJECT_NAME=$PROJECT_NAME" >> $GITHUB_OUTPUT

            # Step 6: Zip the build directory
            - name: Zip the build directory
              run: |
                  zip -r ${{ steps.get-project-name.outputs.PROJECT_NAME }}.zip ./dist/*
              shell: bash

            # Step 7: Upload the zip file as an artifact
            - name: Upload the artifact
              uses: actions/upload-artifact@v4
              with:
                  name: ${{ steps.get-project-name.outputs.PROJECT_NAME }}
                  path: ${{ steps.get-project-name.outputs.PROJECT_NAME }}.zip
                  compression-level: 0 # Skip compression since file is already zipped
                  retention-days: 5 # Keep the artifact for 5 days