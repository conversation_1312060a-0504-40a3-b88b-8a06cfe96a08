# Custom Hit Error Bar

An external hit error / UR bar implementation for osu!, powered by [tosu](https://github.com/tosuapp/tosu).  
designed around customization and performance, with a focus on compatibility with osu!mania.  
rewritten in typescript mostly from scratch, and designed to be easy to maintain and extend.

[//]: # (TODO: update preview upon successful production build)

## Preview

![preview](./.github/assets/customhiterrorbar.gif)

## Features

- Customizable settings accessible from the tosu dashboard
  - change colors
  - change sizes
  - change opacities
  - change rounded corners
  - and more!
- Tick lifetimes are now adjustable via settings, no longer determined by tick count.
- standard deviation display (toggled in settings).

## How to download

1. Open Tosu Dashboard
2. open `Avaiable` tab
3. find `Custom Hit Error Bar`
4. click `Download`

### OBS instructions

1. Open tosu dashboard
2. click on URL button to copy URL
3. Open OBS
4. create new browser source
5. paste URL
6. paste Resolution width and height (940 x 150)

### Ingame Overlay instructions

1. Open tosu dashboard
2. open settings tab
3. set `ENABLE_INGAME_OVERLAY` to `enabled`
4. restart both osu! and tosu
5. press `Ctrl` + `Shift` + `Space` to open ingame overlay manager
6. right click anywhere and select `Custom Hit Error Bar` in the dropdown menu

### Contributing

Any contributions are welcome!  
Feel free to open an issue or a pull request.
See [CONTRIBUTING.md](https://github.com/breadles5/customHitErrorBar/blob/main/CONTRIBUTING.md) for more information.
