name: build for osu-counters

on:
    pull_request:
        branches:
            - "CI" # Only run on PRs targeting the CI branch
        types: [opened, synchronize] # Run when PR is opened or updated

jobs:
    build:
        runs-on: ubuntu-latest # Use Ubuntu for the build environment

        steps:
            # Step 1: Checkout the repository
            - name: Checkout code
              uses: actions/checkout@v4

            # Step 2: Set up pnpm
            - name: Set up pnpm
              uses: pnpm/action-setup@v4
              with:
                  version: 10
                  run_install: true

            # Step 3: Install Node.js
            - name: Install Node.js
              uses: actions/setup-node@v4
              with:
                node-version: 20
                cache: 'pnpm'

            # Step 4: Install dependencies (uses cache if available)
            - name: Install dependencies
              run: pnpm install --frozen-lockfile

            # Step 5: Build the project
            - name: Build the project
              run: pnpm run build:ci

            # Step 6: Checkout target repository
            - name: Checkout target repository
              uses: actions/checkout@v4
              with:
                  repository: ${{ github.actor }}/counters # Checkout the user's fork
                  token: ${{ secrets.TARGET_REPO_PAT }} # PAT secret for authentication
                  path: deploy_target # Checkout to a temporary directory

            # Step 7: Sync target repo with upstream (runs inside deploy_target)
            - name: Sync target repository with upstream
              working-directory: deploy_target # Set working directory for this step
              run: |
                git config user.name 'github-actions[bot]'
                git config user.email 'github-actions[bot]@users.noreply.github.com'
                echo "Adding upstream remote..."
                # Add upstream if it doesn't exist
                git remote add upstream https://github.com/tosuapp/counters.git || echo "Upstream remote already exists."
                echo "Fetching upstream master branch..."
                git fetch upstream master
                echo "Merging upstream/master into current branch (master)..."
                # Merge upstream/master into the current branch (should be master).
                # Fail the workflow if merge conflicts occur.
                git merge upstream/master --allow-unrelated-histories -m "Merge upstream/master into master"
                # No push needed here; the commit step will push the merged changes + new build files to a new branch.
              shell: bash


            # Step 8: Copy build files to target repository
            - name: Copy build files
              run: |
                  TARGET_DIR="deploy_target/counters/customhiterrorbar by breadles5"
                  mkdir -p "$TARGET_DIR" # Ensure the target folder exists
                  # echo "Cleaning target directory..." # Removed cleaning step
                  # rm -rf "$TARGET_DIR"/* # Removed cleaning step
                  echo "Copying build files..."
                  cp -r ./dist/* "$TARGET_DIR"/ # Copy build output
              shell: bash

            # Step 9: Commit and push changes to a new branch in the fork
            - name: Commit and push changes to new branch
              id: commit_push
              working-directory: deploy_target # Set working directory for this step
              run: |
                  # Configure git user
                  git config user.name 'github-actions[bot]'
                  git config user.email 'github-actions[bot]@users.noreply.github.com'

                  # Define a unique branch name for this update
                  BRANCH_NAME="ci-update-${{ github.run_id }}-${{ github.sha }}"
                  echo "Creating and checking out branch: $BRANCH_NAME"
                  git checkout -b $BRANCH_NAME

                  # Add the updated counter files
                  git add "counters/customhiterrorbar by breadles5"/*

                  # Commit changes if any exist
                  # Use 'git diff --staged --quiet' to check for staged changes
                  if ! git diff --staged --quiet; then
                    echo "Committing changes..."
                    git commit -m "Update counters from build job ${{ github.run_id }} (${{ github.sha }})"
                    echo "Pushing branch $BRANCH_NAME to origin..."
                    # Push the new branch to the fork repository (origin)
                    git push origin $BRANCH_NAME
                  else
                    echo "No changes to commit."
                  fi

                  # Output the branch name for the PR step
                  echo "branch_name=$BRANCH_NAME" >> $GITHUB_OUTPUT
              shell: bash

            # Step 10: Create Pull Request using GitHub CLI from the new branch
            - name: Create Pull Request using GitHub CLI
              # Only run if the commit_push step actually created a branch (i.e., had changes)
              if: steps.commit_push.outputs.branch_name
              env:
                  GH_TOKEN: ${{ secrets.TARGET_REPO_PAT }} # Use the PAT for authentication
                  GH_REPO: tosuapp/counters # Target repository
                  # Use the dynamically created branch name from the previous step
                  HEAD_BRANCH: ${{ github.actor }}:${{ steps.commit_push.outputs.branch_name }}
                  BASE_BRANCH: main # Or the default branch of tOSUapp/counters
                  PR_TITLE: "new: custom hit error bar" # Set fixed PR title
                  # --- Set fixed PR body with image URLs ---
                  PR_BODY: |
                    ![customhiterrorbar.png](https://raw.githubusercontent.com/${{ github.repository }}/${{ github.sha }}/.github/assets/customhiterrorbar.png)
                    ![customhiterrorbar.gif](https://raw.githubusercontent.com/${{ github.repository }}/${{ github.sha }}/.github/assets/customhiterrorbar.gif)

                    tosu
              run: |
                  gh pr create \
                    --repo "$GH_REPO" \
                    --base "$BASE_BRANCH" \
                    --head "$HEAD_BRANCH" \
                    --title "$PR_TITLE" \
                    --body "$PR_BODY"
              shell: bash
