<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <link rel="stylesheet" href="style.css" />
    </head>
    <body>
        <div id="container" class="hidden">
            <!-- now added by js -->
            <!-- <div class="sd hidden"></div> -->
            <div class="arrow-container hidden">
                <div class="arrow hidden"></div>
            </div>
            <div class="main hidden">
                <div class="colors-container hidden"><!-- THIS IS A FLEX CONTAINER  --></div>
                <div class="tick-container hidden"></div>
                <div class="bar hidden"></div>
            </div>
        </div>
        <script type="module" src="src/index.ts"></script>
    </body>
</html>
